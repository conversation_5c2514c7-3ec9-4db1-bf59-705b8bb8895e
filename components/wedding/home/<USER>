<template>
    <WebSection as="footer" class="py-16 text-gray-500 bg-gray-100">
        <WebContainer>
            <!-- Top Bar -->
            <div class="grid gap-8 sm:grid-cols-2 lg:grid-cols-4 lg:gap-16">
                <!-- Company Info -->
                <div class="sm:col-span-2">
                    <WebLogo />

                    <h2 class="mt-6 mb-2 text-xl font-bold text-pink-500">
                        Thi<PERSON><PERSON> cưới online – Đơn gi<PERSON>n, tinh tế, đậm dấu ấn riêng!
                    </h2>
                    <p class="max-w-md text-gray-500">
                        Nền tảng tạo thiệp cưới online miễn phí, giúp bạn hiện thực hóa lời mời cưới vừa tiện lợi vừa
                        tinh tế.
                    </p>

                    <!-- Contact Information -->
                    <div class="mt-6 space-y-3">
                        <h3 class="text-lg font-semibold text-pink-500">Thông tin liên hệ</h3>

                        <!-- Address -->
                        <div class="flex items-center gap-3">
                            <MapPin class="flex-shrink-0 text-pink-500" :size="16" />
                            <div>
                                <span class="font-medium text-gray-700">Địa chỉ:</span>
                                <span class="text-gray-500">
                                    72/53 Nguyễn Văn Thương, Phường 25, Quận Bình Thạnh, TP.HCM
                                </span>
                            </div>
                        </div>

                        <!-- Email -->
                        <div class="flex items-center gap-3">
                            <Mail class="flex-shrink-0 text-pink-500" :size="16" />
                            <div>
                                <span class="font-medium text-gray-700">Email:</span>
                                <a
                                    href="mailto:<EMAIL>"
                                    class="text-gray-500 transition-colors duration-200 hover:text-pink-500 hover:underline"
                                >
                                    <EMAIL>
                                </a>
                            </div>
                        </div>

                        <!-- Hotlines -->
                        <div class="flex items-center gap-3">
                            <Phone class="flex-shrink-0 text-pink-500" :size="16" />
                            <div>
                                <span class="font-medium text-gray-700">Hotline 1:</span>
                                <a
                                    href="tel:02835366566"
                                    class="text-gray-500 transition-colors duration-200 hover:text-pink-500 hover:underline"
                                >
                                    (028).353.66566
                                </a>
                            </div>
                        </div>
                        <div class="flex items-center gap-3">
                            <Phone class="flex-shrink-0 text-pink-500" :size="16" />
                            <div>
                                <span class="font-medium text-gray-700">Hotline 2:</span>
                                <a
                                    href="tel:02822366566"
                                    class="text-gray-500 transition-colors duration-200 hover:text-pink-500 hover:underline"
                                >
                                    (028).223.66566
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Navs -->
                <div v-for="(nav, index) in footerNavs" :key="index">
                    <h3 class="mb-4 text-lg font-semibold text-pink-500">{{ nav.title }}</h3>
                    <nav>
                        <ul>
                            <li v-for="(navItem, i) in nav.navMenu" :key="i">
                                <NuxtLink
                                    class="block py-1 text-gray-500 transition-colors duration-200 hover:text-pink-500 hover:underline"
                                    v-bind="reactiveOmit(navItem, 'text')"
                                >
                                    {{ navItem.text }}
                                </NuxtLink>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="pt-8 mt-8 border-t border-gray-500">
                <p class="text-sm text-gray-500">Copyrights © 2025 E-Wedding by CTPG. All rights reserved</p>
            </div>
        </WebContainer>
    </WebSection>
</template>

<script setup lang="ts">
import { reactiveOmit } from "@vueuse/core";
import { MapPin, Mail, Phone } from "lucide-vue-next";
import type { LandingFooterData } from "@/components/wedding/types";

const props = defineProps<{
    data: LandingFooterData;
}>();

const { navMenu, policiesNavMenu } = props.data;
const footerNavs = [
    { title: "Liên kết nhanh", navMenu },
    { title: "Hỗ trợ", navMenu: policiesNavMenu },
];
</script>
